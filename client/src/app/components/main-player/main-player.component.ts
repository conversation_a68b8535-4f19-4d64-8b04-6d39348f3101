import { RepeatState } from '@/enums/repeatState'
import { environment } from '@/env/environment'
import { Track } from '@/interfaces/track'
import { PlaylistDialogComponent } from '@/pages/audio-gallery/playlist-dialog/playlist-dialog.component'
import { IsInPlaylistPipe } from '@/pipes/isInPlaylist.pipe'
import { AudioService } from "@/services/audio.service"
import { AuthService } from '@/services/auth.service'
import { PlaylistService } from '@/services/playlist.service'
import { ProfileService } from "@/services/profile.service"
import { ShareDataService } from '@/services/share-data.service'
import { ToasterService } from '@/services/toaster.service'
import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common'
import { ChangeDetectorRef, Component, ElementRef, EventEmitter, HostListener, inject, Inject, OnDestroy, OnInit, Output, PLATFORM_ID, Renderer2, signal, ViewChild } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { Router, RouterLink } from '@angular/router'
import { TranslocoService } from '@jsverse/transloco'
import { NgSelectModule } from "@ng-select/ng-select"
import { filter, interval, take } from 'rxjs'
import { FavoritesIconComponent } from "../svg-components/favorites-icon/favorites-icon.component"

@Component({
  selector: 'app-main-player',
  standalone: true,
  imports: [CommonModule, FormsModule, NgSelectModule,
    IsInPlaylistPipe, RouterLink, PlaylistDialogComponent, FavoritesIconComponent],
  templateUrl: './main-player.component.html',
  styleUrls: ['./main-player.component.scss']
})
export class MainPlayerComponent implements OnInit, OnDestroy {
  @HostListener('window:resize')
  onResize() {
    this.updateScale();
  }
  @ViewChild('navContner', { static: false }) navContner!: ElementRef;
  @Output() sideOpen = new EventEmitter<any>();
  currentLanguage = signal<string>('ru');
  isBackgroundPlay: boolean = false;
  readonly MAX_WIDTH = 500;
  readonly MID_WIDTH = 375;
  readonly MIN_WIDTH = 320;
  readonly MAX_SCALE = 1.4;
  readonly MID_SCALE = 1.0;
  readonly MIN_SCALE = 0.9;
  body: any = null;
  profileService = inject(ProfileService);
  audioService = inject(AudioService);
  toasterService = inject(ToasterService);
  repeatState: RepeatState = RepeatState.NoRepeat;
  authService = inject(AuthService);
  speedOptions = [1, 1.3, 1.5, 1.7, 2];
  audio: HTMLAudioElement | null = null;
  currentTrackIndex: number = 0;
  progressBarWidth: string = '0';
  isShuffle: boolean = false;
  isPlaying: boolean = false;
  isPressed: boolean = false;
  showList: boolean = false;
  currentSpeed = 1;
  seekTimeout: any = null;
  currentTime: number = 0;
  duration: number = 0;
  volume: number = 0.5;
  isRadio: boolean = false;
  isRadioPlaying: boolean = false;
  changeDetector = inject(ChangeDetectorRef);
  router = inject(Router);
  radioLink: string = '';
  tracks: Track[] = [];
  draggedIndex: number | null = null;
  currentTrackKey: string | null = null;
  currentTrackId: number | null = null;
  timeUpdating = false
  listened: any = []
  emptyPlayer: boolean = false;
  playlistService = inject(PlaylistService);
  selectedPlaylists: any = [];
  selectedTrackId: number | null = null;
  translocoService = inject(TranslocoService);

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    @Inject(DOCUMENT) private document: Document,
    private renderer2: Renderer2,
    private shareDataService: ShareDataService
  ) { }

  ngOnInit() {
    if (isPlatformBrowser(this.platformId)) {
      if (this.authService.isAuth) {
        this.profileService.getPlaylist().subscribe((res: any) => {
          this.playlists = res;
        });
      }
    }
    this.shareDataService.playCard$.subscribe(() => {
      this.isBackgroundPlay = false;
      if (this.isPlaying) {
        if (this.audio) {
          this.audio.pause();
          this.isPlaying = false;
        }
      }
      this.lockBody();
    })
    this.shareDataService.addToPlaylist$.subscribe((item) => {
      this.isBackgroundPlay = false;
      if (item) {
        // if (item.play) {
        //   this.currentTrackId = item.track.id;
        //   this.tracks.unshift(item.track);
        //   this.playTrack(1);
        // } else if (!item.play) {
        //   this.currentTrackId = item.track.id;
        //   this.tracks.push(item.track);
        // } else if (!this.tracks.length) {
        //   this.currentTrackId = item.track.id;
        //   this.tracks.push(item.track);
        //   this.playTrack(1);
        // } else {
        //   this.currentTrackId = item.track.id;
        //   this.tracks.push(item.track);
        // }
        if (!this.tracks.length) {
          this.currentTrackId = item.track.id;
          this.tracks.push(item.track);
          this.playTrack(1);
        } else if (item.play) {
          this.currentTrackId = item.track.id;
          this.tracks.unshift(item.track);
          this.playTrack(1);
        } else {
          this.currentTrackId = item.track.id;
          this.tracks.push(item.track);
        }
      }
      this.lockBody();
    });
    this.shareDataService.playRadio$.subscribe((item) => {
      this.isBackgroundPlay = false;
      if (item) {
        if (item.play) {
          this.currentTrackId = item.radio.id;
          if (!this.isRadio) {
            this.tracks.unshift(item.radio);
          }
          this.playTrack(-1, item.radio.link);
        }
      }
      this.lockBody();
    });
    this.shareDataService.changeTracks$.subscribe((items: Track[]) => {
      if(!items) return;
      items = items.filter((e: any) => e.link)
      if(!items.length) return;

      this.isBackgroundPlay = false;
      if (items) {
        this.currentTrackIndex = this.tracks.length;
        this.isPlaying = false;
        this.currentTime = 0;
        this.tracks = this.tracks.concat(items);
        this.playTrack();
        this.savePlayerState();
      }
      this.lockBody();
    })
    if (isPlatformBrowser(this.platformId)) {
      this.audio = new Audio();
      this.addAudioEventListeners();
      this.loadPlayerState();
    }
  }

  ngOnDestroy() {
    if (this.audio) {
      this.audio.removeEventListener('timeupdate', this.updateTime);
      this.audio.removeEventListener('loadedmetadata', this.updateDuration);
      this.audio.removeEventListener('ended', this.onTrackEnd);
      this.audio.removeEventListener('volumechange', this.updateVolumeProgress);
      this.audio.removeEventListener('error', this.onAudioError);
      this.audio.removeEventListener('loadstart', this.onLoadStart);
    }
  }

  convertSecondsToTime(seconds: string) {
    const hours = Math.floor(+seconds / 3600);
    const minutes = Math.floor((+seconds % 3600) / 60);
    const secs = +seconds % 60;
    return `${this.padZero(hours)}:${this.padZero(minutes)}:${this.padZero(secs)}`;
  }

  navigateToLibrary() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/library`]);
  }

  navigateToAudio() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/audiogallery/audiolektsii`]);
  }

  toggleSidebar() {
    this.sideOpen.emit(true);
  }

  padZero(value: number) {
    return value < 10 ? `0${value}` : `${value}`;
  }

  onDragStart(index: number, event: DragEvent) {
    event.dataTransfer?.setData('text/plain', index.toString());
    this.draggedIndex = index;
    this.currentTrackKey = this.tracks[this.currentTrackIndex]?.external_id || null;
    event.stopPropagation();
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
  }

  onDrop(event: DragEvent, dropIndex: number) {
    event.preventDefault();
    if (this.draggedIndex !== null && this.draggedIndex !== dropIndex) {
      const draggedItem = this.tracks[this.draggedIndex];
      this.tracks.splice(this.draggedIndex, 1);
      this.tracks.splice(dropIndex, 0, draggedItem);
    }
    this.draggedIndex = null;
    if (this.currentTrackKey !== this.tracks[this.currentTrackIndex]?.external_id) {
      this.currentTrackIndex = this.tracks.findIndex(trk => trk.external_id === this.currentTrackKey);
    }
  }

  selectTrack(index: number) {
    this.currentTrackIndex = index;
    this.currentTrackId = this.tracks[this.currentTrackIndex]?.id
    this.listened.splice(0, this.listened.length)
    this.playTrack();
    this.savePlayerState();
  }

  setPlaybackSpeed(speed: number) {
    if (speed == 1.3) {
      this.currentSpeed = 1.25;
    } else if (speed == 1.7) {
      this.currentSpeed = 1.75;
    } else {
      this.currentSpeed = speed;
    }
    if (this.audio) {
      this.audio.playbackRate = this.currentSpeed;
    }
  }

  addAudioEventListeners() {
    if (this.audio) {
      this.audio.addEventListener('timeupdate', this.updateTime.bind(this));
      this.audio.addEventListener('loadedmetadata', this.updateDuration.bind(this));
      this.audio.addEventListener('ended', this.onTrackEnd.bind(this));
      this.audio.addEventListener('volumechange', this.updateVolumeProgress.bind(this));
      this.audio.addEventListener('error', this.onAudioError.bind(this));
      this.audio.addEventListener('loadstart', this.onLoadStart.bind(this));
    }
  }

  onAudioError(event: Event) {
    console.error('Audio error:', event);
    const audio = event.target as HTMLAudioElement;
    if (audio.error) {
      let errorMessage = 'Ошибка воспроизведения аудио';
      switch (audio.error.code) {
        case MediaError.MEDIA_ERR_ABORTED:
          errorMessage = 'Воспроизведение прервано';
          break;
        case MediaError.MEDIA_ERR_NETWORK:
          errorMessage = 'Ошибка сети при загрузке аудио';
          break;
        case MediaError.MEDIA_ERR_DECODE:
          errorMessage = 'Ошибка декодирования аудио';
          break;
        case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
          errorMessage = 'Формат аудио не поддерживается';
          break;
      }
      this.toasterService.showToast(errorMessage, 'error', 'bottom-middle', 3000);
      this.isPlaying = false;

      const source = audio.currentSrc;
      const index = this.tracks.findIndex((track) => track.link === source);
      this.tracks.splice(index, 1);
    }
  }

  onLoadStart() {
    console.log('Audio loading started');
  }

  toggleMute() {
    if (isPlatformBrowser(this.platformId) && this.audio) {
      this.audio.muted = !this.audio.muted;
    }
  }

  closePlayer() {
    if (isPlatformBrowser(this.platformId)) {
      const screenWidth = window.innerWidth;
      if (screenWidth > 500) {
        this.tracks = [];
        if (this.isPlaying && this.audio) {
          this.audio.pause();
        }
        this.isPlaying = !this.isPlaying;
      } else {
        this.isBackgroundPlay = true;
      }
    }
    this.lockBody();
  }

  private updateScale(): void {
    if (isPlatformBrowser(this.platformId)) {
      interval(50).pipe(
        filter(() => !!this.navContner?.nativeElement),
        take(1)
      ).subscribe(() => {
        this.updateScale();
      });
      if (!this.navContner) return;
      const screenWidth = window.innerWidth;
      const element = this.navContner.nativeElement as HTMLElement;
      let scale: number;
      if (window.innerWidth > 500) {
        element.style.transform = `scale(1)`;
        element.style.transformOrigin = 'top center';
        return;
      }
      if (screenWidth >= this.MAX_WIDTH) {
        scale = this.MAX_SCALE;
      } else if (screenWidth >= this.MID_WIDTH) {
        const ratio = (screenWidth - this.MID_WIDTH) / (this.MAX_WIDTH - this.MID_WIDTH);
        scale = this.MID_SCALE + (this.MAX_SCALE - this.MID_SCALE) * ratio;
      } else if (screenWidth >= this.MIN_WIDTH) {
        const ratio = (screenWidth - this.MIN_WIDTH) / (this.MID_WIDTH - this.MIN_WIDTH);
        scale = this.MIN_SCALE + (this.MID_SCALE - this.MIN_SCALE) * ratio;
      } else {
        scale = this.MIN_SCALE;
      }
      element.style.transform = `scale(${scale})`;
      element.style.transformOrigin = 'top center';
    }
  }

  togglePlay() {
    this.updateScale();
    this.emptyPlayer = false;
    if (isPlatformBrowser(this.platformId) && this.audio) {
      if (this.isPlaying) {
        this.audio.pause();
      } else {
        if (!this.audio.src) {
          // Check if track exists and has a valid link
          if (this.tracks[this.currentTrackIndex]?.link) {
            this.audio.src = this.tracks[this.currentTrackIndex].link;
          } else {
            console.error('No valid track link available');
            this.toasterService.showToast('Ошибка: недоступный аудиофайл', 'error', 'bottom-middle', 3000);
            return;
          }
        }
        this.audio.play().then(() => {
          this.shareDataService.playMainPlayer();
          this.audio!.volume = this.volume;
          this.setPlaybackSpeed(this.currentSpeed);
        }).catch((error) => {
          console.error('Audio play error:', error);
          this.toasterService.showToast('Ошибка воспроизведения аудио', 'error', 'bottom-middle', 3000);
          this.isPlaying = false;
        });
      }
      this.isPlaying = !this.isPlaying;
      this.savePlayerState();
    }
  }

  updateTime() {
    if (isPlatformBrowser(this.platformId) && this.audio) {
      this.currentTime = this.audio.currentTime;
      const progressPercentage = (this.audio.currentTime / this.audio.duration) * 100;
      this.progressBarWidth = `${progressPercentage}%`;
      if (!this.timeUpdating && Math.ceil(this.currentTime) % 2 === 0) {
        this.timeUpdating = true;

        // Save to localStorage
        this.shareDataService.updatePlayerTime(this.currentTime);

        this.audioService.savePosition({
          user_id: this.profileService.profile?.id,
          audio_id: this.currentTrackId,
          time: this.currentTime
        }).subscribe(() => {
          setTimeout(() => this.timeUpdating = false, 1000)
        })
        const progress = Math.ceil(Math.ceil(this.currentTime) / Math.ceil(this.duration) * 100);
        if (progress > 90 && !this.listened.includes(this.currentTrackId)) {
          this.audioService.setAsListened(this.currentTrackId!).subscribe(() => this.listened.push(this.currentTrackId));
        }
      }
    }
  }

  updateDuration() {
    if (isPlatformBrowser(this.platformId) && this.audio) {
      this.duration = this.audio.duration;
    }
  }

  onTrackEnd() {
    if (this.repeatState === RepeatState.RepeatOne) {
      this.playTrack();
    } else if (this.repeatState === RepeatState.RepeatAll || this.isShuffle) {
      this.nextTrack();
    } else if (this.currentTrackIndex < this.tracks.length - 1) {
      this.nextTrack();
    } else {
      if (this.currentTrackIndex < this.tracks.length - 1) {
        this.nextTrack();
      } else {
        if (this.audio) {
          this.audio.pause();
        }
        this.isPlaying = false;
        this.currentTrackIndex = 0; // Optionally reset to first track
      }
    }
  }

  nextTrack() {
    this.listened.splice(0, this.listened.length);
    if (this.repeatState === RepeatState.RepeatOne) {
      this.playTrack();
    } else {
      if (this.isShuffle) {
        let newTrackIndex: number;
        do {
          newTrackIndex = Math.floor(Math.random() * this.tracks.length);
        } while (newTrackIndex === this.currentTrackIndex && this.tracks.length > 1);
        this.currentTrackIndex = newTrackIndex;
      } else {
        this.currentTrackIndex = (this.currentTrackIndex + 1) % this.tracks.length;
      }
      if (this.currentTrackIndex === 0 && this.repeatState === RepeatState.RepeatAll) {
        this.currentTrackIndex = 0;
      }
      this.playTrack();
      this.savePlayerState();
    }
  }

  shuffleArray(array: any[]) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array.map((track, index) => ({ ...track, index }));
  }

  prevTrack() {
    this.currentTrackIndex =
      this.currentTrackIndex === 0 ? this.tracks.length - 1 : this.currentTrackIndex - 1;
    this.playTrack();
    this.savePlayerState();
  }

  lockBody(lock?: boolean) {
    if (isPlatformBrowser(this.platformId)) {
      const screenWidth = window.innerWidth;
      if (screenWidth <= 500) {
        if (!this.body) {
          this.body = this.document.body;
        }
        if (lock) {
          this.renderer2.setStyle(this.body, 'overflow', 'hidden');
        } else {
          if (!this.isBackgroundPlay && this.tracks.length) {
            this.renderer2.setStyle(this.body, 'overflow', 'hidden');
          } else {
            this.renderer2.setStyle(this.body, 'overflow', 'auto');
          }
        }
      }
    }
  }

  playTrack(index?: number, link?: string) {
    this.updateScale();
    this.emptyPlayer = false;
    if (isPlatformBrowser(this.platformId) && this.audio) {
      if ((this.currentTrackIndex == 1) && this.tracks.length == 1) {
        this.currentTrackIndex = 0;
      }
      // TODO SAVE POSITION
      // if (!link) {
      //   this.listened = false
      //   this.audioService.getSavedPosition({
      //     user_id: this.profileService.profile?.id,
      //     audio_id: this.currentTrackId,
      //   }).subscribe((res: any) => this.audio!.currentTime = parseFloat(res))
      // }

      let url: string | null = null;

      if (link) {
        url = link;
        this.radioLink = link;
        this.isRadioPlaying = true;
      } else {
        const trackIndex = index !== undefined ? 0 : this.currentTrackIndex;
        const track = this.tracks[trackIndex];

        if (!track?.link) {
          console.error('No valid track or link available');
          this.toasterService.showToast('Ошибка: недоступный аудиофайл', 'error', 'bottom-middle', 3000);
          return;
        }

        url = track.link;

        if (url.endsWith('/stream')) {
          this.isRadioPlaying = true;
          this.changeDetector.detectChanges();
        } else {
          this.isRadioPlaying = false;
          this.changeDetector.detectChanges();
        }
      }

      if (url) {
        this.audio.src = url;
        this.setPlaybackSpeed(this.currentSpeed);
        this.audio.play().then(() => {
          this.isPlaying = true;
        }).catch((error) => {
          console.error('Audio play error:', error);
          this.toasterService.showToast('Ошибка воспроизведения аудио', 'error', 'bottom-middle', 3000);
          this.isPlaying = false;
        });
      }
    }
  }

  toggleRepeatState() {
    if (this.repeatState === RepeatState.NoRepeat) {
      this.repeatState = RepeatState.RepeatAll;
    } else if (this.repeatState === RepeatState.RepeatAll) {
      this.repeatState = RepeatState.RepeatOne;
    } else {
      this.repeatState = RepeatState.NoRepeat;
    }
  }

  clear() {
    if (isPlatformBrowser(this.platformId) && this.audio) {
      this.tracks = [];
      if (this.isPlaying) {
        this.audio.pause();
      }
      this.isPlaying = false;
      this.emptyPlayer = true;
    }
    this.currentTime = 0;
    this.isBackgroundPlay = false;
    this.showList = false;
    this.shareDataService.clearPlayerState();
    this.lockBody();
  }

  addToPlaylist(track: Track) {
    this.shareDataService.addToPlaylist(track);
    this.toasterService.showToast('Лекция добавлена в плейлист!', 'success', 'bottom-middle', 3000);
  }


  addToPlaylistProfile() {
    for (let playlist of this.selectedPlaylists) {
      this.playlistService.add(playlist, this.selectedTrackId).subscribe(() => {
        this.selectedPlaylists = []
        this.toasterService.showToast('Лекция добавлена в Избранное', 'success', 'bottom-middle', 3000);
      })
    }
  }

  remove(i: number) {
    //   if (isPlatformBrowser(this.platformId) && this.audio) {
    //     let track = this.tracks.splice(i, 1);
    //     if (this.tracks.length) {
    //       if (this.audio.src == track[0].link) {
    //         if (i == this.tracks.length) {
    //           this.currentTrackIndex = 0;
    //         }
    //         this.playTrack();
    //         this.togglePlay();
    //       }
    //       if (i < this.currentTrackIndex) {
    //         this.currentTrackIndex = this.currentTrackIndex - 1;
    //       }
    //     } else {
    //       if (this.isPlaying) {
    //         this.audio.pause();
    //       }
    //       this.isPlaying = false;
    //       this.emptyPlayer = true;
    //     }
    //     this.currentTime = 0;
    //   }
  }

  share(track: any) {
    console.log(track);
    const lang = this.translocoService.getActiveLang();

    navigator.clipboard.writeText(`${environment.baseUrl}/${lang}/audiogallery/audiolektsii/${track.external_id}`).then(() => {
      this.toasterService.showToast('Ссылка на аудио скопирована в буфер обмена!', 'success', 'bottom-middle', 3000)
    }).catch((error) => {
      console.error('Unable to copy text to clipboard', error);
    });
  }

  getRepeatClass(): string {
    if (this.repeatState === RepeatState.RepeatAll) {
      return 'repeat-all';
    } else if (this.repeatState === RepeatState.RepeatOne) {
      return 'repeat-one';
    } else {
      return 'no-repeat';
    }
  }

  shuffle() {
    if (this.repeatState === RepeatState.RepeatOne && !this.isShuffle) return;
    this.isShuffle = !this.isShuffle;
    if (this.isShuffle) {
      this.tracks = this.shuffleArray(this.tracks);
    }
  }

  formatTime(time: number): string {
    const minutes: number = Math.floor(time / 60);
    const seconds: number = Math.floor(time % 60);
    if (`${minutes}:${seconds < 10 ? '0' : ''}${seconds}` == 'Infinity:NaN') {
      if (!this.isRadio) {
        this.isRadio = true;
        this.changeDetector.detectChanges();
      }
      this.isRadio = true;
      return '∞';
    } else {
      return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
    }
  }

  get progressPercentage(): number {
    return (this.currentTime / this.duration) * 100 || 0;
  }

  private getZoomFactor(): number {
    // Check if we're in a zoomed container
    const contentWrapper = document.querySelector('.content-height_wrap:not(.main-contetnt-wrapper)');
    if (contentWrapper) {
      return 0.8; // zoom: 0.8 from styles.scss
    }
    return 1;
  }

  seekTo(event: MouseEvent) {
    if (this.audio && this.audio.duration && !this.isRadioPlaying) {
      const progressWrapper = event.target as HTMLElement;
      const zoomFactor = this.getZoomFactor();

      // Use offsetX but adjust for zoom factor
      let clickX = event.offsetX;

      // Adjust for zoom factor - when zoomed to 0.8, offsetX is compressed
      if (zoomFactor !== 1) {
        clickX = clickX / zoomFactor;
      }

      const progressWidth = progressWrapper.clientWidth;
      const newTime = (clickX / progressWidth) * this.audio.duration;
      this.audio.currentTime = newTime;
    }
  }

  seekToDebounced(event: MouseEvent) {
    if (this.seekTimeout) {
      clearTimeout(this.seekTimeout);
    }
    this.seekTimeout = setTimeout(() => {
      this.seekTo(event);
    }, 100);
  }

  changeVolume(event: any) {
    if (isPlatformBrowser(this.platformId) && this.audio) {
      if (this.audio.muted) {
        this.audio.muted = false;
      }
      this.audio.volume = event.target.value;
      this.volume = event.target.value;
    }
  }

  updateVolumeProgress() {
    const volumeProgress = document.querySelector('.volume__sliderProgress') as HTMLElement | null;
    const volumeHandle = document.querySelector('.volume__sliderHandle') as HTMLElement | null;
    if (volumeProgress && volumeHandle) {
      if (isPlatformBrowser(this.platformId) && this.audio) {
        const volumePercent = this.audio.volume * 100;
        volumeProgress.style.height = `calc(${volumePercent}% - 20%)`;
        volumeHandle.style.top = `${100 - volumePercent}%`;
      }
    }
  }

  forward30Seconds() {
    if (isPlatformBrowser(this.platformId) && this.audio) {
      const newTime = this.audio.currentTime + 30;
      this.audio.currentTime = Math.min(newTime, this.audio.duration);
    }
  }

  backward30Seconds() {
    if (isPlatformBrowser(this.platformId) && this.audio) {
      const newTime = this.audio.currentTime - 30;
      this.audio.currentTime = Math.max(newTime, 0);
    }
  }

  addToFavourites(track?: Track) {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.audioService.addToFavourites(track ? track.id : this.tracks[this.currentTrackIndex]?.id).subscribe((r) => {
      if ((r as any).audio) {
        if (track) {
          track.inFavourites = true;
        } else {
          this.tracks[this.currentTrackIndex].inFavourites = true;
        }
        this.toasterService.showToast('Лекция добавлена в избранное!', 'success', 'bottom-middle', 3000);
      } else {
        if (track) {
          track.inFavourites = false;
        } else {
          this.tracks[this.currentTrackIndex].inFavourites = false;
        }
        this.toasterService.showToast('Лекция удалена из избранного!', 'success', 'bottom-middle', 3000);
      }
    })
  }

  like(track: any) {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
      this.audioService.like(track.id).subscribe((r) => {
        if ((r as any).audio) {
          track.liked = true;
          // this.toasterService.showToast('Лекция добавлена в понравившееся!', 'success', 'bottom-middle', 3000);
        } else {
          track.liked = false;
          // this.toasterService.showToast('Лекция удалена из понравившихся!', 'success', 'bottom-middle', 3000);
        }
      })
  }

  showPlaylist = false;
  playlists = []

  showPlaylistDialog() {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.showPlaylist = true;

    this.selectedTrackId = this.tracks[this.currentTrackIndex]?.id;
  }

  playlistClosed(event: any) {
    this.showPlaylist = event;
  }

  playlistSelected(playlists: any) {
    this.showPlaylist = false;

    this.playlists = playlists;
  }

  // LocalStorage methods
  savePlayerState() {
    if (isPlatformBrowser(this.platformId)) {
      const state = {
        currentTrack: this.tracks[this.currentTrackIndex] || null,
        currentTrackIndex: this.currentTrackIndex,
        currentTime: this.currentTime,
        queue: this.tracks,
        isPlaying: this.isPlaying
      };
      this.shareDataService.savePlayerState(state);
    }
  }

  loadPlayerState() {
    if (isPlatformBrowser(this.platformId)) {
      const savedState = this.shareDataService.loadPlayerState();
      if (savedState && savedState.queue.length > 0) {
        this.tracks = savedState.queue;
        this.currentTrackIndex = savedState.currentTrackIndex;
        this.currentTime = savedState.currentTime;
        this.currentTrackId = savedState.currentTrack?.id || null;

        // Restore audio source if there's a current track
        if (this.audio && savedState.currentTrack?.link) {
          this.audio.src = savedState.currentTrack.link;
          this.audio.currentTime = savedState.currentTime;

          // If was playing, restore playing state
          if (savedState.isPlaying) {
            this.isPlaying = true;
            // Don't auto-play, just set the state
          }
        }

        this.changeDetector.detectChanges();
      }
    }
  }

  removeTrack(i: number) {
    this.tracks.splice(i, 1);
  }
}
